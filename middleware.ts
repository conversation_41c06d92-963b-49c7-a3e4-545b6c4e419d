import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if user is on a protected route (dashboard)
  const isOnDashboard = pathname.startsWith("/dashboard")

  // Check for session token cookie (NextAuth uses this for JWT sessions)
  const sessionToken =
    request.cookies.get("next-auth.session-token") || request.cookies.get("__Secure-next-auth.session-token")

  const isLoggedIn = !!sessionToken

  // Redirect to sign-in if accessing dashboard without authentication
  if (isOnDashboard && !isLoggedIn) {
    const signInUrl = new URL("/sign-in", request.url)
    return NextResponse.redirect(signInUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|.*\\.png$).*)"],
}
