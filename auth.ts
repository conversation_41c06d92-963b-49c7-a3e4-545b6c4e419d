import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import type { NextAuthOptions } from "next-auth"
import { z } from "zod"

async function authenticateUser(email: string, password: string) {
  try {
    const response = await fetch("https://portfolio-mamun-api.vercel.app/api/v1/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Authentication error:", error)
    return null
  }
}

export const authConfig: NextAuthOptions = {
  pages: {
    signIn: "/sign-in",
  },
  secret: process.env.AUTH_SECRET,
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string
      }
      return session
    },
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const parsedCredentials = z
          .object({ email: z.string().email(), password: z.string().min(6) })
          .safeParse(credentials)

        if (parsedCredentials.success) {
          const { email, password } = parsedCredentials.data
          const userData = await authenticateUser(email, password)

          if (!userData) return null

          // Return user data from API response
          return {
            id: userData.user?.id || userData.id || email,
            email: userData.user?.email || email,
            name: userData.user?.name || userData.name || email.split("@")[0],
          }
        }

        return null
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
}

const handler = NextAuth(authConfig)
export { handler }
